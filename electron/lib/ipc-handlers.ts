import { ipcMain, dialog } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import { figmaApiRequest, parseFigmaUrl } from './figma-api'
import { downloadImages } from './image-downloader'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器 - 只负责纯粹的API请求，不处理数据
  ipcMain.handle('figma-api-request', async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers })
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (_event, url: string) => {
    return await parseFigmaUrl(url)
  })

  // 图片下载处理器
  ipcMain.handle('download-images', async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath })
  })

  // 文件夹选择对话框处理器
  ipcMain.handle('show-folder-dialog', async (_event) => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择生成文件夹',
        buttonLabel: '选择文件夹'
      })

      if (result.canceled) {
        return {
          success: false,
          canceled: true
        }
      }

      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '选择文件夹失败'
      }
    }
  })

  // 验证并创建文件夹处理器
  ipcMain.handle('validate-and-create-folder', async (_event, folderPath: string) => {
    try {
      // 检查路径是否存在
      if (fs.existsSync(folderPath)) {
        // 检查是否是文件夹
        const stats = fs.statSync(folderPath)
        if (stats.isDirectory()) {
          return {
            success: true,
            data: {
              exists: true,
              isDirectory: true,
              message: '文件夹已存在'
            }
          }
        } else {
          return {
            success: false,
            error: '指定路径是一个文件，不是文件夹'
          }
        }
      } else {
        // 文件夹不存在，尝试创建
        try {
          fs.mkdirSync(folderPath, { recursive: true })
          return {
            success: true,
            data: {
              exists: false,
              created: true,
              message: '文件夹创建成功'
            }
          }
        } catch (createError) {
          return {
            success: false,
            error: `创建文件夹失败: ${createError instanceof Error ? createError.message : '未知错误'}`
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `验证文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  })

  // 初始化项目文件夹处理器
  ipcMain.handle('init-project', async (_event, { folderPath, domContent }: { folderPath: string, domContent?: string }) => {
    return await initProjectFolder(folderPath, domContent)
  })
}

/**
 * 初始化项目文件夹
 * 1. 创建文件夹
 * 2. 复制template.html到新文件夹并重命名为index.html
 * 3. 如果提供了DOM内容，将其插入到index.html中
 * 4. 创建assets子目录
 */
async function initProjectFolder(folderPath: string, domContent?: string): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    // 1. 创建主文件夹（如果不存在）
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true })
      console.log(`📁 创建文件夹: ${folderPath}`)
    }

    // 2. 复制template.html到新文件夹并重命名为index.html
    const templatePath = path.join(process.cwd(), 'template.html')
    const targetIndexPath = path.join(folderPath, 'index.html')

    if (!fs.existsSync(templatePath)) {
      throw new Error('template.html文件不存在于项目根目录')
    }

    // 读取模板内容
    let templateContent = fs.readFileSync(templatePath, 'utf-8')

    // 如果提供了DOM内容，将其插入到body标签中
    if (domContent) {
      templateContent = templateContent.replace('<body>', `<body>\n  ${domContent}`)
      console.log('📄 已将DOM结构插入到index.html中')
    }

    // 写入index.html文件
    fs.writeFileSync(targetIndexPath, templateContent, 'utf-8')
    console.log(`📄 创建index.html: ${targetIndexPath}`)

    // 3. 创建assets子目录
    const assetsPath = path.join(folderPath, 'assets')
    if (!fs.existsSync(assetsPath)) {
      fs.mkdirSync(assetsPath, { recursive: true })
      console.log(`📁 创建assets目录: ${assetsPath}`)
    }

    return {
      success: true,
      data: {
        folderPath,
        indexPath: targetIndexPath,
        assetsPath,
        message: '项目文件夹初始化成功'
      }
    }

  } catch (error) {
    console.error('❌ 初始化项目文件夹失败:', error)
    return {
      success: false,
      error: `初始化项目文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

