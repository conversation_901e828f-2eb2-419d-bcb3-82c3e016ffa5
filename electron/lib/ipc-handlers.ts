import { ipcMain, dialog } from 'electron'
import { figmaApiRequest, parseFigmaUrl } from './figma-api'
import { downloadImages } from './image-downloader'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器 - 只负责纯粹的API请求，不处理数据
  ipcMain.handle('figma-api-request', async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers })
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (_event, url: string) => {
    return await parseFigmaUrl(url)
  })

  // 图片下载处理器
  ipcMain.handle('download-images', async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath })
  })

  // 文件夹选择对话框处理器
  ipcMain.handle('show-folder-dialog', async (_event) => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择生成文件夹',
        buttonLabel: '选择文件夹'
      })

      if (result.canceled) {
        return {
          success: false,
          canceled: true
        }
      }

      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '选择文件夹失败'
      }
    }
  })
}
