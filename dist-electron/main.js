"use strict";
const electron = require("electron");
const node_module = require("node:module");
const node_url = require("node:url");
const path$1 = require("node:path");
const fs = require("fs");
const https = require("https");
const http = require("http");
const querystring = require("querystring");
const path = require("path");
var _documentCurrentScript = typeof document !== "undefined" ? document.currentScript : null;
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const https__namespace = /* @__PURE__ */ _interopNamespaceDefault(https);
const http__namespace = /* @__PURE__ */ _interopNamespaceDefault(http);
const querystring__namespace = /* @__PURE__ */ _interopNamespaceDefault(querystring);
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const parsedUrl = new URL(url);
      const httpModule = parsedUrl.protocol === "https:" ? https__namespace : http__namespace;
      if (options.params) {
        const queryString = querystring__namespace.stringify(options.params);
        if (queryString) {
          parsedUrl.search = parsedUrl.search ? `${parsedUrl.search}&${queryString}` : `?${queryString}`;
        }
      }
      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === "https:" ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || "GET",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "Electron-App/1.0.0",
          ...options.headers
        },
        timeout: options.timeout || 1e4
      };
      const req = httpModule.request(requestOptions, (res) => {
        const chunks = [];
        res.on("data", (chunk) => {
          chunks.push(chunk);
        });
        res.on("end", () => {
          try {
            const statusCode = res.statusCode || 0;
            if (statusCode >= 400) {
              reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`));
              return;
            }
            const responseBuffer = Buffer.concat(chunks);
            let parsedData;
            if (options.responseType === "buffer") {
              parsedData = responseBuffer;
            } else {
              const responseData = responseBuffer.toString("utf8");
              const contentType = res.headers["content-type"] || "";
              if (options.responseType === "json" || contentType.includes("application/json") && responseData.trim()) {
                try {
                  parsedData = JSON.parse(responseData);
                } catch (parseError) {
                  parsedData = responseData;
                }
              } else {
                parsedData = responseData;
              }
            }
            resolve({
              data: parsedData,
              status: statusCode,
              statusText: res.statusMessage || "OK",
              headers: res.headers
            });
          } catch (error) {
            reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : "Unknown error"}`));
          }
        });
      });
      req.on("error", (error) => {
        reject(new Error(`Network error: ${error.message}`));
      });
      req.on("timeout", () => {
        req.destroy();
        reject(new Error(`Request timeout after ${requestOptions.timeout}ms`));
      });
      if (options.data && options.method !== "GET") {
        const postData = typeof options.data === "object" ? JSON.stringify(options.data) : String(options.data);
        req.write(postData);
      }
      req.end();
    } catch (error) {
      reject(new Error(`Request error: ${error instanceof Error ? error.message : "Unknown error"}`));
    }
  });
}
const FIGMA_TOKEN = "*********************************************";
async function figmaApiRequest({ url, params, headers }) {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        "X-FIGMA-TOKEN": FIGMA_TOKEN,
        ...headers
      }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function parseFigmaUrl(url) {
  try {
    const parsedUrl = new URL(url);
    if (!parsedUrl.hostname.includes("figma.com")) {
      throw new Error("不是有效的 Figma 链接");
    }
    const pathParts = parsedUrl.pathname.split("/").filter((part) => part.length > 0);
    if (pathParts.length < 2 || !["file", "design"].includes(pathParts[0])) {
      throw new Error("无效的 Figma 链接格式");
    }
    const fileKey = pathParts[1];
    let nodeId;
    const nodeIdParam = parsedUrl.searchParams.get("node-id");
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(":", "-");
    }
    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function downloadImages({ imageUrls, downloadPath }) {
  try {
    if (!fs__namespace.existsSync(downloadPath)) {
      fs__namespace.mkdirSync(downloadPath, { recursive: true });
    }
    const filePaths = [];
    const downloadPromises = [];
    for (const [nodeId, imageUrl] of Object.entries(imageUrls)) {
      if (!imageUrl) continue;
      const downloadPromise = downloadSingleImage(nodeId, imageUrl, downloadPath).then((filePath) => {
        if (filePath) {
          filePaths.push(filePath);
        }
      }).catch((error) => {
        console.error(`下载图片 ${nodeId} 失败:`, error);
      });
      downloadPromises.push(downloadPromise);
    }
    await Promise.all(downloadPromises);
    return {
      success: true,
      data: {
        filePaths
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function downloadSingleImage(nodeId, imageUrl, downloadPath) {
  try {
    const response = await makeHttpRequest(imageUrl, {
      responseType: "buffer"
    });
    const fileName = `${nodeId.replace(":", "-")}.png`;
    const filePath = path__namespace.join(downloadPath, fileName);
    fs__namespace.writeFileSync(filePath, response.data);
    console.log(`图片下载成功: ${fileName}`);
    return filePath;
  } catch (error) {
    console.error(`下载图片 ${nodeId} 失败:`, error);
    return null;
  }
}
function setupIpcHandlers() {
  electron.ipcMain.handle("figma-api-request", async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers });
  });
  electron.ipcMain.handle("parse-figma-url", async (_event, url) => {
    return await parseFigmaUrl(url);
  });
  electron.ipcMain.handle("download-images", async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath });
  });
  electron.ipcMain.handle("show-folder-dialog", async (_event) => {
    try {
      const result = await electron.dialog.showOpenDialog({
        properties: ["openDirectory"],
        title: "选择生成文件夹",
        buttonLabel: "选择文件夹"
      });
      if (result.canceled) {
        return {
          success: false,
          canceled: true
        };
      }
      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "选择文件夹失败"
      };
    }
  });
  electron.ipcMain.handle("validate-and-create-folder", async (_event, folderPath) => {
    try {
      if (fs__namespace.existsSync(folderPath)) {
        const stats = fs__namespace.statSync(folderPath);
        if (stats.isDirectory()) {
          return {
            success: true,
            data: {
              exists: true,
              isDirectory: true,
              message: "文件夹已存在"
            }
          };
        } else {
          return {
            success: false,
            error: "指定路径是一个文件，不是文件夹"
          };
        }
      } else {
        try {
          fs__namespace.mkdirSync(folderPath, { recursive: true });
          return {
            success: true,
            data: {
              exists: false,
              created: true,
              message: "文件夹创建成功"
            }
          };
        } catch (createError) {
          return {
            success: false,
            error: `创建文件夹失败: ${createError instanceof Error ? createError.message : "未知错误"}`
          };
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `验证文件夹失败: ${error instanceof Error ? error.message : "未知错误"}`
      };
    }
  });
}
node_module.createRequire(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href);
const __dirname$1 = path$1.dirname(node_url.fileURLToPath(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href));
process.env.DIST = path$1.join(__dirname$1, "../dist");
process.env.VITE_PUBLIC = electron.app.isPackaged ? process.env.DIST : path$1.join(process.env.DIST, "../public");
let win;
function createWindow() {
  win = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    icon: path$1.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path$1.join(__dirname$1, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(path$1.join(process.env.DIST, "index.html"));
  }
}
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
    win = null;
  }
});
electron.app.on("activate", () => {
  if (electron.BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
electron.app.whenReady().then(() => {
  createWindow();
  setupIpcHandlers();
});
